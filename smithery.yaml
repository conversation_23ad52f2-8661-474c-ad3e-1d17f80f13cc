# Smithery configuration file: https://smithery.ai/docs/deployments

build:
  dockerBuildPath: .
startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - e2bApiKey
    properties:
      e2bApiKey:
        type: string
        description: The API key for the E2B server.
  commandFunction:
    # A function that produces the CLI command to start the MCP on stdio.
    |-
    (config) => ({ command: 'node', args: ['./build/index.js'], env: { E2B_API_KEY: config.e2bApiKey } })
